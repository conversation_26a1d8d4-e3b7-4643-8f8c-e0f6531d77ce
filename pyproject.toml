[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "discord-selfbot-python"
version = "1.0.0"
description = "A high-performance Discord self-bot built with Python for educational purposes"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
license = "MIT"
packages = [{include = "src"}]

[tool.poetry.dependencies]
python = "^3.9"
discord-py = "^2.3.2"
pydantic = "^2.5.0"
pydantic-settings = "^2.1.0"
aiohttp = "^3.9.0"
python-dotenv = "^1.0.0"
rich = "^13.7.0"
asyncio-throttle = "^1.0.2"

# Optional performance dependencies
uvloop = {version = "^0.19.0", markers = "sys_platform != 'win32'"}
orjson = "^3.9.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-asyncio = "^0.21.0"
pytest-cov = "^4.1.0"
pytest-mock = "^3.12.0"
mypy = "^1.8.0"
black = "^23.12.0"
isort = "^5.13.0"
flake8 = "^6.1.0"
flake8-docstrings = "^1.7.0"
pre-commit = "^3.6.0"

[tool.poetry.scripts]
discord-selfbot = "discord_selfbot.main:main"
token-checker = "discord_selfbot.utils.validators:main"

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["discord_selfbot"]
known_third_party = ["discord", "pydantic", "aiohttp"]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true

[[tool.mypy.overrides]]
module = "discord.*"
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
asyncio_mode = "auto"
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/virtualenv/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
show_missing = true
precision = 2

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503", "E501"]
exclude = [
    ".git",
    "__pycache__",
    "docs/source/conf.py",
    "old",
    "build",
    "dist",
    ".venv",
    "venv",
]
per-file-ignores = [
    "__init__.py:F401",
    "tests/*:D",
]
